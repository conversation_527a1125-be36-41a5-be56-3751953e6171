<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة مصنع الشنط</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- شاشة تسجيل الدخول -->
    <div id="loginScreen" class="login-container">
        <div class="login-card">
            <div class="text-center mb-4">
                <i class="bi bi-bag-fill login-icon"></i>
                <h2 class="login-title">نظام إدارة مصنع الشنط</h2>
                <p class="text-muted">تسجيل دخول المدير</p>
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    يجب أن تكون مديراً للوصول إلى النظام
                </div>
            </div>
            <form id="loginForm">
                <div class="form-group mb-3">
                    <label for="username" class="form-label">اسم المدير</label>
                    <input type="text" class="form-control neumorphic-input" id="username"
                           placeholder="admin" required>
                </div>
                <div class="form-group mb-4">
                    <label for="password" class="form-label">كلمة مرور المدير</label>
                    <input type="password" class="form-control neumorphic-input" id="password"
                           placeholder="كلمة المرور" required>
                </div>
                <button type="submit" class="btn btn-primary neumorphic-btn w-100">
                    <i class="bi bi-shield-check me-2"></i>
                    دخول كمدير
                </button>

                <!-- زر مفتاح الترخيص -->
                <button type="button" id="licenseKeyBtn" class="btn btn-outline-warning neumorphic-btn w-100 mt-2" onclick="showLicenseModal()">
                    <i class="bi bi-key-fill me-2"></i>
                    <span id="licenseKeyText">مفتاح الترخيص</span>
                </button>
            </form>
            <div class="text-center mt-3">
                <small class="text-muted">
                    <i class="bi bi-lock me-1"></i>
                    يتطلب تسجيل الدخول في كل مرة لضمان الأمان
                </small>
            </div>
            <div class="text-center mt-2">
                <small class="text-muted">
                    تصميم وإعداد: البشمهندس أحمد يونس | الهاتف: 01100693019
                </small>
            </div>
        </div>
    </div>

    <!-- الواجهة الرئيسية -->
    <div id="mainApp" class="d-none">
        <!-- شريط التنقل العلوي -->
        <nav class="navbar navbar-expand-lg neumorphic-navbar">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="bi bi-bag-fill me-2"></i>
                    نظام إدارة مصنع الشنط
                </a>
                <div class="navbar-nav ms-auto">
                    <span class="navbar-text me-3">
                        مرحباً، <span id="currentUser"></span>
                    </span>
                    <button class="btn btn-outline-danger btn-sm" onclick="logout()">
                        <i class="bi bi-box-arrow-right"></i>
                        تسجيل الخروج
                    </button>
                </div>
            </div>
        </nav>

        <!-- القائمة الجانبية -->
        <div class="sidebar neumorphic-sidebar">
            <div class="sidebar-menu">
                <div class="menu-item active" onclick="showSection('dashboard')">
                    <i class="bi bi-speedometer2"></i>
                    <span>لوحة التحكم</span>
                </div>
                <div class="menu-item" onclick="showSection('products')">
                    <i class="bi bi-bag"></i>
                    <span>إدارة المنتجات</span>
                </div>
                <div class="menu-item" onclick="showSection('inventory')">
                    <i class="bi bi-boxes"></i>
                    <span>المخزون</span>
                </div>
                <div class="menu-item" onclick="showSection('workers')">
                    <i class="bi bi-people"></i>
                    <span>إدارة العمال</span>
                </div>
                <div class="menu-item" onclick="showSection('attendance')">
                    <i class="bi bi-calendar-check"></i>
                    <span>الحضور والانصراف</span>
                </div>
                <div class="menu-item" onclick="showSection('payroll')">
                    <i class="bi bi-cash-coin"></i>
                    <span>المرتبات</span>
                </div>
                <div class="menu-item" onclick="showSection('reports')">
                    <i class="bi bi-graph-up"></i>
                    <span>التقارير</span>
                </div>
                <div class="menu-item" onclick="showSection('materials')">
                    <i class="bi bi-tags"></i>
                    <span>الخامات والتصنيفات</span>
                </div>
                <div class="menu-item" onclick="showSection('settings')">
                    <i class="bi bi-gear"></i>
                    <span>الإعدادات</span>
                </div>
            </div>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="main-content">
            <!-- لوحة التحكم -->
            <div id="dashboard" class="content-section active">
                <div class="page-header">
                    <h1><i class="bi bi-speedometer2 me-2"></i>لوحة التحكم</h1>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-4">
                        <div class="stat-card neumorphic-card">
                            <div class="stat-icon bg-primary">
                                <i class="bi bi-bag"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="totalProducts">0</h3>
                                <p>إجمالي المنتجات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-4">
                        <div class="stat-card neumorphic-card">
                            <div class="stat-icon bg-success">
                                <i class="bi bi-boxes"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="totalStock">0</h3>
                                <p>إجمالي المخزون</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-4">
                        <div class="stat-card neumorphic-card">
                            <div class="stat-icon bg-info">
                                <i class="bi bi-people"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="totalWorkers">0</h3>
                                <p>عدد العمال</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-4">
                        <div class="stat-card neumorphic-card">
                            <div class="stat-icon bg-warning">
                                <i class="bi bi-cash-coin"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="monthlyPayroll">0 ج.م</h3>
                                <p>مرتبات الشهر</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- بطاقة معلومات المشترك -->
                <div id="customerLicenseCard" class="row mb-4" style="display: none;">
                    <div class="col-12">
                        <div class="neumorphic-card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="bi bi-person-badge me-2"></i>
                                    معلومات المشترك
                                </h5>
                                <span id="licenseStatusBadge" class="badge bg-success">مشترك</span>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="info-item">
                                            <i class="bi bi-person-fill text-primary me-2"></i>
                                            <strong>الاسم:</strong>
                                            <span id="dashboardCustomerName">-</span>
                                        </div>
                                        <div class="info-item">
                                            <i class="bi bi-telephone-fill text-success me-2"></i>
                                            <strong>الهاتف:</strong>
                                            <span id="dashboardCustomerPhone">-</span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="info-item">
                                            <i class="bi bi-envelope-fill text-info me-2"></i>
                                            <strong>البريد الإلكتروني:</strong>
                                            <span id="dashboardCustomerEmail">-</span>
                                        </div>
                                        <div class="info-item">
                                            <i class="bi bi-calendar-check text-warning me-2"></i>
                                            <strong>تاريخ التفعيل:</strong>
                                            <span id="dashboardActivationDate">-</span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="info-item">
                                            <i class="bi bi-calendar-x text-danger me-2"></i>
                                            <strong>تاريخ الانتهاء:</strong>
                                            <span id="dashboardExpirationDate">-</span>
                                        </div>
                                        <div class="info-item">
                                            <i class="bi bi-hourglass-split text-primary me-2"></i>
                                            <strong>المدة المتبقية:</strong>
                                            <span id="dashboardRemainingDays" class="fw-bold text-success">-</span>
                                        </div>
                                        <div class="info-item">
                                            <i class="bi bi-award text-secondary me-2"></i>
                                            <strong>نوع الترخيص:</strong>
                                            <span id="dashboardLicenseType">-</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-center mt-3">
                                    <button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="showLicenseModal()">
                                        <i class="bi bi-key me-2"></i>
                                        إدارة الترخيص
                                    </button>
                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="showCancelSubscriptionModal()">
                                        <i class="bi bi-x-circle me-2"></i>
                                        إلغاء الاشتراك
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسوم البيانية -->
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="neumorphic-card">
                            <div class="card-header">
                                <h5><i class="bi bi-bar-chart me-2"></i>الإنتاج الأسبوعي</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="productionChart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="neumorphic-card">
                            <div class="card-header">
                                <h5><i class="bi bi-pie-chart me-2"></i>حضور العمال</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="attendanceChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسوم البيانية الإضافية -->
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="neumorphic-card">
                            <div class="card-header">
                                <h5><i class="bi bi-graph-up me-2"></i>سحب العمال الأسبوعي</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="workerWithdrawalsChart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="neumorphic-card">
                            <div class="card-header">
                                <h5><i class="bi bi-activity me-2"></i>حركة المخزون الزمنية</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="inventoryMovementChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إدارة المنتجات -->
            <div id="products" class="content-section">
                <div class="page-header">
                    <h1><i class="bi bi-bag me-2"></i>إدارة المنتجات</h1>
                    <button class="btn btn-primary neumorphic-btn" onclick="showAddProductModal()">
                        <i class="bi bi-plus-circle me-2"></i>إضافة منتج جديد
                    </button>
                </div>
                
                <!-- شريط البحث والفلترة -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="search-box">
                            <input type="text" class="form-control neumorphic-input" id="productSearch"
                                   placeholder="البحث بالاسم أو الباركود...">
                            <i class="bi bi-search search-icon"></i>
                            <button type="button" class="btn btn-outline-primary search-scanner-btn"
                                    id="searchScannerBtn" onclick="startSearchBarcodeScanner()"
                                    title="البحث بالماسح الضوئي">
                                <i class="bi bi-upc-scan"></i>
                            </button>
                        </div>
                        <small class="text-muted" id="searchScannerStatus" style="display: none;">
                            <i class="bi bi-circle-fill text-success me-1"></i>
                            الماسح الضوئي نشط - امسح الباركود للبحث
                        </small>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select neumorphic-input" id="categoryFilter">
                            <option value="">جميع التصنيفات</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select neumorphic-input" id="sortBy">
                            <option value="name">ترتيب بالاسم</option>
                            <option value="price">ترتيب بالسعر</option>
                            <option value="quantity">ترتيب بالكمية</option>
                        </select>
                    </div>
                </div>

                <!-- جدول المنتجات -->
                <div class="neumorphic-card">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الكود</th>
                                    <th>الاسم</th>
                                    <th>الخامة</th>
                                    <th>التصنيف</th>
                                    <th>السعر</th>
                                    <th>الكمية</th>
                                    <th>إجمالي السعر</th>
                                    <th>الباركود</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="productsTable">
                                <!-- سيتم ملء البيانات بواسطة JavaScript -->
                            </tbody>
                        </table>
                    </div>

                    <!-- نظام الصفحات للمنتجات -->
                    <div class="pagination-container d-flex justify-content-between align-items-center" id="productsPagination" style="display: none;">
                        <div class="pagination-info">
                            <i class="bi bi-box-seam me-2"></i>
                            عرض <span id="productsShowingStart">1</span> إلى <span id="productsShowingEnd">5</span> من <span id="productsTotalItems">0</span> منتج
                        </div>
                        <div class="pagination-controls d-flex align-items-center">
                            <button class="btn btn-sm neumorphic-btn me-1" id="productsFirstBtn" onclick="goToProductsPage(1)" disabled>
                                <i class="bi bi-chevron-double-right"></i>
                            </button>
                            <button class="btn btn-sm neumorphic-btn" id="productsPrevBtn" onclick="app.changeProductsPage(-1)" disabled>
                                <i class="bi bi-chevron-right"></i> السابق
                            </button>
                            <span class="mx-2 text-muted">
                                صفحة <span id="productsCurrentPage">1</span> من <span id="productsTotalPages">1</span>
                            </span>
                            <button class="btn btn-sm neumorphic-btn me-1" id="productsNextBtn" onclick="app.changeProductsPage(1)" disabled>
                                التالي <i class="bi bi-chevron-left"></i>
                            </button>
                            <button class="btn btn-sm neumorphic-btn" id="productsLastBtn" onclick="goToProductsPage(app.pagination.products.totalPages)" disabled>
                                <i class="bi bi-chevron-double-left"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إدارة المخزون -->
            <div id="inventory" class="content-section">
                <div class="page-header">
                    <h1><i class="bi bi-boxes me-2"></i>إدارة المخزون</h1>
                    <div class="d-flex gap-2">
                        <button class="btn btn-success neumorphic-btn" onclick="showAddStockModal()">
                            <i class="bi bi-plus-circle me-2"></i>إضافة كمية للمخزون
                        </button>
                        <button class="btn btn-warning neumorphic-btn" onclick="showWorkerWithdrawalModal()">
                            <i class="bi bi-person-dash me-2"></i>سحب عامل من المخزون
                        </button>
                    </div>
                </div>

                <!-- إحصائيات المخزون -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="stat-card neumorphic-card">
                            <div class="stat-icon bg-info">
                                <i class="bi bi-boxes"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="totalStockValue">0 ج.م</h3>
                                <p>قيمة المخزون الإجمالية</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stat-card neumorphic-card">
                            <div class="stat-icon bg-warning">
                                <i class="bi bi-exclamation-triangle"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="lowStockItems">0</h3>
                                <p>منتجات تحتاج تجديد</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stat-card neumorphic-card">
                            <div class="stat-icon bg-success">
                                <i class="bi bi-box-arrow-down"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="todayProduction">0</h3>
                                <p>إجمالي الكمية المسحوبة</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول المخزون -->
                <div class="neumorphic-card mb-4">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>كود المنتج</th>
                                    <th>اسم المنتج</th>
                                    <th>الكمية الحالية</th>
                                    <th>الحد الأدنى</th>
                                    <th>آخر إنتاج</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="inventoryTable">
                                <!-- سيتم ملء البيانات بواسطة JavaScript -->
                            </tbody>
                        </table>
                    </div>

                    <!-- نظام الصفحات للمخزون -->
                    <div class="pagination-container d-flex justify-content-between align-items-center" id="inventoryPagination" style="display: none;">
                        <div class="pagination-info">
                            <i class="bi bi-info-circle me-2"></i>
                            عرض <span id="inventoryShowingStart">1</span> إلى <span id="inventoryShowingEnd">5</span> من <span id="inventoryTotalItems">0</span> عنصر
                        </div>
                        <div class="pagination-controls d-flex align-items-center">
                            <button class="btn btn-sm neumorphic-btn me-1" id="inventoryFirstBtn" onclick="goToInventoryPage(1)" disabled>
                                <i class="bi bi-chevron-double-right"></i>
                            </button>
                            <button class="btn btn-sm neumorphic-btn" id="inventoryPrevBtn" onclick="app.changeInventoryPage(-1)" disabled>
                                <i class="bi bi-chevron-right"></i> السابق
                            </button>
                            <span class="page-indicator mx-3">
                                صفحة <span id="inventoryCurrentPage">1</span> من <span id="inventoryTotalPages">1</span>
                            </span>
                            <button class="btn btn-sm neumorphic-btn" id="inventoryNextBtn" onclick="app.changeInventoryPage(1)" disabled>
                                التالي <i class="bi bi-chevron-left"></i>
                            </button>
                            <button class="btn btn-sm neumorphic-btn ms-1" id="inventoryLastBtn" onclick="goToInventoryPage(app.pagination.inventory.totalPages)" disabled>
                                <i class="bi bi-chevron-double-left"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- سجلات سحب العمال من المخزون -->
                <div class="neumorphic-card mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5><i class="bi bi-person-dash me-2"></i>سجلات سحب العمال من المخزون</h5>
                        <div class="d-flex gap-2">
                            <input type="text" class="form-control neumorphic-input" id="withdrawalSearch"
                                   placeholder="البحث في سجلات السحب..." style="width: 200px;">
                            <input type="date" class="form-control neumorphic-input" id="withdrawalDateFilter"
                                   placeholder="البحث بالتاريخ..." style="width: 150px;">
                            <select class="form-select neumorphic-input" id="withdrawalWorkerFilter" style="width: 180px;">
                                <option value="">جميع العمال</option>
                            </select>
                            <button class="btn btn-outline-secondary neumorphic-btn" onclick="app.clearWithdrawalFilters()">
                                <i class="bi bi-x-circle me-2"></i>مسح الفلاتر
                            </button>
                            <button class="btn btn-outline-primary neumorphic-btn" onclick="app.printWorkerWithdrawalsReport()">
                                <i class="bi bi-printer me-2"></i>طباعة التقرير
                            </button>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>الوقت</th>
                                    <th>اسم العامل</th>
                                    <th>الوظيفة</th>
                                    <th>المنتج</th>
                                    <th>الكمية</th>
                                    <th>القيمة</th>
                                    <th>ملاحظات</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="workerWithdrawalsTable">
                                <!-- سيتم ملء البيانات بواسطة JavaScript -->
                            </tbody>
                        </table>
                    </div>

                    <!-- نظام الصفحات لسجلات سحب العمال -->
                    <div class="pagination-container d-flex justify-content-between align-items-center" id="withdrawalsPagination" style="display: none;">
                        <div class="pagination-info">
                            <i class="bi bi-person-dash me-2"></i>
                            عرض <span id="withdrawalsShowingStart">1</span> إلى <span id="withdrawalsShowingEnd">5</span> من <span id="withdrawalsTotalItems">0</span> سجل سحب
                        </div>
                        <div class="pagination-controls d-flex align-items-center">
                            <button class="btn btn-sm neumorphic-btn me-1" id="withdrawalsFirstBtn" onclick="goToWithdrawalsPage(1)" disabled>
                                <i class="bi bi-chevron-double-right"></i>
                            </button>
                            <button class="btn btn-sm neumorphic-btn" id="withdrawalsPrevBtn" onclick="app.changeWithdrawalsPage(-1)" disabled>
                                <i class="bi bi-chevron-right"></i> السابق
                            </button>
                            <span class="page-indicator mx-3">
                                صفحة <span id="withdrawalsCurrentPage">1</span> من <span id="withdrawalsTotalPages">1</span>
                            </span>
                            <button class="btn btn-sm neumorphic-btn" id="withdrawalsNextBtn" onclick="app.changeWithdrawalsPage(1)" disabled>
                                التالي <i class="bi bi-chevron-left"></i>
                            </button>
                            <button class="btn btn-sm neumorphic-btn ms-1" id="withdrawalsLastBtn" onclick="goToWithdrawalsPage(app.pagination.withdrawals.totalPages)" disabled>
                                <i class="bi bi-chevron-double-left"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- سجل تتبع حركة المخزون الزمني -->
                <div class="neumorphic-card">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5><i class="bi bi-clock-history me-2"></i>سجل تتبع حركة المخزون الزمني</h5>
                        <div class="d-flex gap-2">
                            <input type="text" class="form-control neumorphic-input" id="inventoryMovementSearch"
                                   placeholder="البحث بكود المنتج..." style="width: 200px;">
                            <input type="date" class="form-control neumorphic-input" id="inventoryMovementDateFrom"
                                   placeholder="من تاريخ..." style="width: 150px;">
                            <input type="date" class="form-control neumorphic-input" id="inventoryMovementDateTo"
                                   placeholder="إلى تاريخ..." style="width: 150px;">
                            <select class="form-select neumorphic-input" id="inventoryMovementTypeFilter" style="width: 150px;">
                                <option value="">جميع العمليات</option>
                                <option value="add">إضافة فقط</option>
                                <option value="remove">سحب فقط</option>
                                <option value="worker_withdrawal">سحب عمال</option>
                            </select>
                            <button class="btn btn-outline-secondary neumorphic-btn" onclick="app.clearInventoryMovementFilters()">
                                <i class="bi bi-x-circle me-2"></i>مسح الفلاتر
                            </button>
                            <button class="btn btn-outline-primary neumorphic-btn" onclick="app.printInventoryMovementReport()">
                                <i class="bi bi-printer me-2"></i>طباعة التقرير
                            </button>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>الوقت</th>
                                    <th>كود المنتج</th>
                                    <th>اسم المنتج</th>
                                    <th>نوع العملية</th>
                                    <th>الكمية السابقة</th>
                                    <th>الكمية المضافة/المسحوبة</th>
                                    <th>الكمية الحالية</th>
                                    <th>المستخدم</th>
                                    <th>ملاحظات</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="inventoryMovementTable">
                                <!-- سيتم ملء البيانات بواسطة JavaScript -->
                            </tbody>
                        </table>
                    </div>

                    <!-- نظام الصفحات لسجل تتبع حركة المخزون -->
                    <div class="pagination-container d-flex justify-content-between align-items-center" id="movementsPagination" style="display: none;">
                        <div class="pagination-info">
                            <i class="bi bi-clock-history me-2"></i>
                            عرض <span id="movementsShowingStart">1</span> إلى <span id="movementsShowingEnd">5</span> من <span id="movementsTotalItems">0</span> حركة
                        </div>
                        <div class="pagination-controls d-flex align-items-center">
                            <button class="btn btn-sm neumorphic-btn me-1" id="movementsFirstBtn" onclick="goToMovementsPage(1)" disabled>
                                <i class="bi bi-chevron-double-right"></i>
                            </button>
                            <button class="btn btn-sm neumorphic-btn" id="movementsPrevBtn" onclick="app.changeMovementsPage(-1)" disabled>
                                <i class="bi bi-chevron-right"></i> السابق
                            </button>
                            <span class="page-indicator mx-3">
                                صفحة <span id="movementsCurrentPage">1</span> من <span id="movementsTotalPages">1</span>
                            </span>
                            <button class="btn btn-sm neumorphic-btn" id="movementsNextBtn" onclick="app.changeMovementsPage(1)" disabled>
                                التالي <i class="bi bi-chevron-left"></i>
                            </button>
                            <button class="btn btn-sm neumorphic-btn ms-1" id="movementsLastBtn" onclick="goToMovementsPage(app.pagination.movements.totalPages)" disabled>
                                <i class="bi bi-chevron-double-left"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إدارة العمال -->
            <div id="workers" class="content-section">
                <div class="page-header">
                    <h1><i class="bi bi-people me-2"></i>إدارة العمال</h1>
                    <button class="btn btn-primary neumorphic-btn" onclick="showAddWorkerModal()">
                        <i class="bi bi-person-plus me-2"></i>إضافة عامل جديد
                    </button>
                </div>

                <!-- جدول العمال -->
                <div class="neumorphic-card">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الرقم</th>
                                    <th>الاسم</th>
                                    <th>الوظيفة</th>
                                    <th>الراتب اليومي</th>
                                    <th>تاريخ التوظيف</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="workersTable">
                                <!-- سيتم ملء البيانات بواسطة JavaScript -->
                            </tbody>
                        </table>
                    </div>

                    <!-- نظام الصفحات للعمال -->
                    <div class="pagination-container d-flex justify-content-between align-items-center" id="workersPagination" style="display: none;">
                        <div class="pagination-info">
                            <i class="bi bi-people me-2"></i>
                            عرض <span id="workersShowingStart">1</span> إلى <span id="workersShowingEnd">5</span> من <span id="workersTotalItems">0</span> عامل
                        </div>
                        <div class="pagination-controls d-flex align-items-center">
                            <button class="btn btn-sm neumorphic-btn me-1" id="workersFirstBtn" onclick="goToWorkersPage(1)" disabled>
                                <i class="bi bi-chevron-double-right"></i>
                            </button>
                            <button class="btn btn-sm neumorphic-btn" id="workersPrevBtn" onclick="app.changeWorkersPage(-1)" disabled>
                                <i class="bi bi-chevron-right"></i> السابق
                            </button>
                            <span class="mx-2 text-muted">
                                صفحة <span id="workersCurrentPage">1</span> من <span id="workersTotalPages">1</span>
                            </span>
                            <button class="btn btn-sm neumorphic-btn me-1" id="workersNextBtn" onclick="app.changeWorkersPage(1)" disabled>
                                التالي <i class="bi bi-chevron-left"></i>
                            </button>
                            <button class="btn btn-sm neumorphic-btn" id="workersLastBtn" onclick="goToWorkersPage(app.pagination.workers.totalPages)" disabled>
                                <i class="bi bi-chevron-double-left"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الحضور والانصراف -->
            <div id="attendance" class="content-section">
                <div class="page-header">
                    <h1><i class="bi bi-calendar-check me-2"></i>الحضور والانصراف</h1>
                    <div>
                        <input type="date" class="form-control neumorphic-input d-inline-block me-2"
                               id="attendanceDate" style="width: auto;">
                        <button class="btn btn-success neumorphic-btn me-2" onclick="markAllPresent()">
                            <i class="bi bi-check-all me-2"></i>تسجيل حضور الكل
                        </button>
                        <button class="btn btn-warning neumorphic-btn me-2" onclick="markAllLeave()">
                            <i class="bi bi-clock me-2"></i>تسجيل انصراف الكل
                        </button>
                        <button class="btn btn-info neumorphic-btn me-2" onclick="printDailyAttendance()">
                            <i class="bi bi-printer me-2"></i>طباعة حضور اليوم
                        </button>
                        <button class="btn btn-primary neumorphic-btn" onclick="printWeeklyAttendance()">
                            <i class="bi bi-calendar-week me-2"></i>طباعة حضور الأسبوع
                        </button>
                    </div>
                </div>

                <!-- إحصائيات الحضور اليومية -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card neumorphic-card">
                            <div class="stat-icon bg-success">
                                <i class="bi bi-person-check"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="todayPresent">0</h3>
                                <p>حاضر اليوم</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card neumorphic-card">
                            <div class="stat-icon bg-danger">
                                <i class="bi bi-person-x"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="todayAbsent">0</h3>
                                <p>غائب اليوم</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card neumorphic-card">
                            <div class="stat-icon bg-warning">
                                <i class="bi bi-clock"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="todayLate">0</h3>
                                <p>متأخر اليوم</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card neumorphic-card">
                            <div class="stat-icon bg-info">
                                <i class="bi bi-door-open"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="todayLeft">0</h3>
                                <p>انصرف اليوم</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول الحضور -->
                <div class="neumorphic-card">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم العامل</th>
                                    <th>الوظيفة</th>
                                    <th>الحالة</th>
                                    <th>وقت الحضور</th>
                                    <th>وقت الانصراف</th>
                                    <th>ساعات العمل</th>
                                    <th>ملاحظات</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="attendanceTable">
                                <!-- سيتم ملء البيانات بواسطة JavaScript -->
                            </tbody>
                        </table>
                    </div>

                    <!-- نظام الصفحات للحضور والانصراف -->
                    <div class="pagination-container d-flex justify-content-between align-items-center" id="attendancePagination" style="display: none;">
                        <div class="pagination-info">
                            <i class="bi bi-calendar-check me-2"></i>
                            عرض <span id="attendanceShowingStart">1</span> إلى <span id="attendanceShowingEnd">5</span> من <span id="attendanceTotalItems">0</span> عامل
                        </div>
                        <div class="pagination-controls d-flex align-items-center">
                            <button class="btn btn-sm neumorphic-btn me-1" id="attendanceFirstBtn" onclick="goToAttendancePage(1)" disabled>
                                <i class="bi bi-chevron-double-right"></i>
                            </button>
                            <button class="btn btn-sm neumorphic-btn" id="attendancePrevBtn" onclick="app.changeAttendancePage(-1)" disabled>
                                <i class="bi bi-chevron-right"></i> السابق
                            </button>
                            <span class="mx-2 text-muted">
                                صفحة <span id="attendanceCurrentPage">1</span> من <span id="attendanceTotalPages">1</span>
                            </span>
                            <button class="btn btn-sm neumorphic-btn me-1" id="attendanceNextBtn" onclick="app.changeAttendancePage(1)" disabled>
                                التالي <i class="bi bi-chevron-left"></i>
                            </button>
                            <button class="btn btn-sm neumorphic-btn" id="attendanceLastBtn" onclick="goToAttendancePage(app.pagination.attendance.totalPages)" disabled>
                                <i class="bi bi-chevron-double-left"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- المرتبات -->
            <div id="payroll" class="content-section">
                <div class="page-header">
                    <h1><i class="bi bi-cash-coin me-2"></i>المرتبات</h1>
                    <div class="d-flex flex-wrap gap-2">
                        <select class="form-select neumorphic-input" id="payrollPeriodType" style="width: auto;">
                            <option value="daily">يومي</option>
                            <option value="weekly">أسبوعي</option>
                            <option value="monthly" selected>شهري</option>
                            <option value="yearly">سنوي</option>
                        </select>
                        <input type="date" class="form-control neumorphic-input" id="payrollStartDate" style="width: auto;">
                        <input type="date" class="form-control neumorphic-input" id="payrollEndDate" style="width: auto;">
                        <select class="form-select neumorphic-input" id="payrollMonth" style="width: auto;">
                            <option value="">اختر الشهر</option>
                        </select>
                        <select class="form-select neumorphic-input" id="payrollYear" style="width: auto;">
                            <option value="">اختر السنة</option>
                        </select>
                        <button class="btn btn-primary neumorphic-btn" onclick="calculatePayroll()">
                            <i class="bi bi-calculator me-2"></i>حساب المرتبات
                        </button>
                        <button class="btn btn-success neumorphic-btn" onclick="addNextMonth()">
                            <i class="bi bi-plus-circle me-2"></i>إضافة الشهر القادم
                        </button>
                        <button class="btn btn-info neumorphic-btn" onclick="addNextYear()">
                            <i class="bi bi-calendar-plus me-2"></i>إضافة السنة القادمة
                        </button>
                    </div>
                </div>

                <!-- إحصائيات المرتبات -->
                <div class="row mb-4">
                    <div class="col-md-2">
                        <div class="stat-card neumorphic-card">
                            <div class="stat-icon bg-success">
                                <i class="bi bi-cash"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="totalPayroll">0 ج.م</h3>
                                <p>إجمالي المرتبات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card neumorphic-card">
                            <div class="stat-icon bg-info">
                                <i class="bi bi-calendar-day"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="workingDays">0</h3>
                                <p>أيام العمل</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card neumorphic-card">
                            <div class="stat-icon bg-warning">
                                <i class="bi bi-person-x"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="absentDays">0</h3>
                                <p>أيام الغياب</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card neumorphic-card">
                            <div class="stat-icon bg-primary">
                                <i class="bi bi-clock-history"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="totalHours">0</h3>
                                <p>إجمالي الساعات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card neumorphic-card">
                            <div class="stat-icon bg-secondary">
                                <i class="bi bi-plus-circle"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="totalBonuses">0 ج.م</h3>
                                <p>إجمالي الحوافز</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card neumorphic-card">
                            <div class="stat-icon bg-danger">
                                <i class="bi bi-dash-circle"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="totalDeductions">0 ج.م</h3>
                                <p>إجمالي الخصومات</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- فلاتر المرتبات -->
                <div class="neumorphic-card mb-4">
                    <h5><i class="bi bi-funnel me-2"></i>فلاتر المرتبات</h5>
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">نوع الفترة</label>
                            <div id="payrollPeriodInfo" class="alert alert-info">
                                <small>اختر نوع الفترة لحساب المرتبات</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">من تاريخ</label>
                            <div class="text-muted">
                                <small id="periodStartInfo">سيتم تحديده تلقائياً</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">إلى تاريخ</label>
                            <div class="text-muted">
                                <small id="periodEndInfo">سيتم تحديده تلقائياً</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">معلومات الفترة</label>
                            <div class="text-success">
                                <small id="periodDaysInfo">عدد الأيام: 0</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول المرتبات -->
                <div class="neumorphic-card">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم العامل</th>
                                    <th>الراتب اليومي</th>
                                    <th>أيام الحضور</th>
                                    <th>أيام الغياب</th>
                                    <th>الحوافز</th>
                                    <th>الخصومات</th>
                                    <th>صافي المرتب</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="payrollTable">
                                <!-- سيتم ملء البيانات بواسطة JavaScript -->
                            </tbody>
                        </table>
                    </div>

                    <!-- نظام الصفحات للرواتب -->
                    <div class="pagination-container d-flex justify-content-between align-items-center" id="payrollPagination" style="display: none;">
                        <div class="pagination-info">
                            <i class="bi bi-cash-coin me-2"></i>
                            عرض <span id="payrollShowingStart">1</span> إلى <span id="payrollShowingEnd">5</span> من <span id="payrollTotalItems">0</span> راتب
                        </div>
                        <div class="pagination-controls d-flex align-items-center">
                            <button class="btn btn-sm neumorphic-btn me-1" id="payrollFirstBtn" onclick="goToPayrollPage(1)" disabled>
                                <i class="bi bi-chevron-double-right"></i>
                            </button>
                            <button class="btn btn-sm neumorphic-btn" id="payrollPrevBtn" onclick="app.changePayrollPage(-1)" disabled>
                                <i class="bi bi-chevron-right"></i> السابق
                            </button>
                            <span class="mx-2 text-muted">
                                صفحة <span id="payrollCurrentPage">1</span> من <span id="payrollTotalPages">1</span>
                            </span>
                            <button class="btn btn-sm neumorphic-btn me-1" id="payrollNextBtn" onclick="app.changePayrollPage(1)" disabled>
                                التالي <i class="bi bi-chevron-left"></i>
                            </button>
                            <button class="btn btn-sm neumorphic-btn" id="payrollLastBtn" onclick="goToPayrollPage(app.pagination.payroll.totalPages)" disabled>
                                <i class="bi bi-chevron-double-left"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- التقارير -->
            <div id="reports" class="content-section">
                <div class="page-header">
                    <h1><i class="bi bi-graph-up me-2"></i>التقارير</h1>
                    <div>
                        <button class="btn btn-success neumorphic-btn me-2" onclick="exportToExcel()">
                            <i class="bi bi-file-earmark-excel me-2"></i>تصدير Excel
                        </button>
                        <button class="btn btn-danger neumorphic-btn" onclick="exportToPDF()">
                            <i class="bi bi-file-earmark-pdf me-2"></i>تصدير PDF
                        </button>
                    </div>
                </div>

                <!-- أنواع التقارير -->
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="neumorphic-card">
                            <h5><i class="bi bi-bag me-2"></i>تقارير المنتجات</h5>
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-primary" onclick="generateProductReport()">
                                    تقرير المنتجات الكامل
                                </button>
                                <button class="btn btn-outline-warning" onclick="generateLowStockReport()">
                                    تقرير المخزون المنخفض
                                </button>
                                <button class="btn btn-outline-info" onclick="generateWorkerWithdrawalsReport()">
                                    تقرير سحب العمال
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="neumorphic-card">
                            <h5><i class="bi bi-people me-2"></i>تقارير العمال</h5>
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-primary" onclick="generateWorkersReport()">
                                    تقرير العمال الكامل
                                </button>
                                <button class="btn btn-outline-success" onclick="generateAttendanceReport()">
                                    تقرير الحضور والغياب
                                </button>
                                <button class="btn btn-outline-warning" onclick="generatePayrollReport()">
                                    تقرير المرتبات الشهري
                                </button>
                                <button class="btn btn-outline-info" onclick="generateWorkerWithdrawalsReport()">
                                    تقرير سحب العمال من المخزون
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إدارة الخامات والتصنيفات -->
            <div id="materials" class="content-section">
                <div class="page-header">
                    <h1><i class="bi bi-tags me-2"></i>إدارة الخامات والتصنيفات</h1>
                </div>

                <div class="row">
                    <!-- إدارة الخامات -->
                    <div class="col-md-12 mb-4">
                        <div class="neumorphic-card">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5><i class="bi bi-tags me-2"></i>إدارة الخامات</h5>
                                <button class="btn btn-success neumorphic-btn" onclick="showAddMaterialModal()">
                                    <i class="bi bi-plus-circle me-2"></i>إضافة خامة
                                </button>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>اسم الخامة</th>
                                            <th>الوصف</th>
                                            <th>عدد المنتجات</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="materialsTable">
                                        <!-- سيتم ملء البيانات بواسطة JavaScript -->
                                    </tbody>
                                </table>
                            </div>

                            <!-- نظام الصفحات للمواد الخام -->
                            <div class="pagination-container d-flex justify-content-between align-items-center" id="materialsPagination" style="display: none;">
                                <div class="pagination-info">
                                    <i class="bi bi-tags me-2"></i>
                                    عرض <span id="materialsShowingStart">1</span> إلى <span id="materialsShowingEnd">5</span> من <span id="materialsTotalItems">0</span> خامة
                                </div>
                                <div class="pagination-controls d-flex align-items-center">
                                    <button class="btn btn-sm neumorphic-btn me-1" id="materialsFirstBtn" onclick="goToMaterialsPage(1)" disabled>
                                        <i class="bi bi-chevron-double-right"></i>
                                    </button>
                                    <button class="btn btn-sm neumorphic-btn" id="materialsPrevBtn" onclick="app.changeMaterialsPage(-1)" disabled>
                                        <i class="bi bi-chevron-right"></i> السابق
                                    </button>
                                    <span class="mx-2 text-muted">
                                        صفحة <span id="materialsCurrentPage">1</span> من <span id="materialsTotalPages">1</span>
                                    </span>
                                    <button class="btn btn-sm neumorphic-btn me-1" id="materialsNextBtn" onclick="app.changeMaterialsPage(1)" disabled>
                                        التالي <i class="bi bi-chevron-left"></i>
                                    </button>
                                    <button class="btn btn-sm neumorphic-btn" id="materialsLastBtn" onclick="goToMaterialsPage(app.pagination.materials.totalPages)" disabled>
                                        <i class="bi bi-chevron-double-left"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- إدارة التصنيفات -->
                    <div class="col-md-12 mb-4">
                        <div class="neumorphic-card">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5><i class="bi bi-collection me-2"></i>إدارة التصنيفات</h5>
                                <button class="btn btn-success neumorphic-btn" onclick="showAddCategoryModal()">
                                    <i class="bi bi-plus-circle me-2"></i>إضافة تصنيف
                                </button>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>اسم التصنيف</th>
                                            <th>الوصف</th>
                                            <th>عدد المنتجات</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="categoriesTable">
                                        <!-- سيتم ملء البيانات بواسطة JavaScript -->
                                    </tbody>
                                </table>
                            </div>

                            <!-- نظام الصفحات للتصنيفات -->
                            <div class="pagination-container d-flex justify-content-between align-items-center" id="categoriesPagination" style="display: none;">
                                <div class="pagination-info">
                                    <i class="bi bi-collection me-2"></i>
                                    عرض <span id="categoriesShowingStart">1</span> إلى <span id="categoriesShowingEnd">5</span> من <span id="categoriesTotalItems">0</span> تصنيف
                                </div>
                                <div class="pagination-controls d-flex align-items-center">
                                    <button class="btn btn-sm neumorphic-btn me-1" id="categoriesFirstBtn" onclick="goToCategoriesPage(1)" disabled>
                                        <i class="bi bi-chevron-double-right"></i>
                                    </button>
                                    <button class="btn btn-sm neumorphic-btn" id="categoriesPrevBtn" onclick="app.changeCategoriesPage(-1)" disabled>
                                        <i class="bi bi-chevron-right"></i> السابق
                                    </button>
                                    <span class="mx-2 text-muted">
                                        صفحة <span id="categoriesCurrentPage">1</span> من <span id="categoriesTotalPages">1</span>
                                    </span>
                                    <button class="btn btn-sm neumorphic-btn me-1" id="categoriesNextBtn" onclick="app.changeCategoriesPage(1)" disabled>
                                        التالي <i class="bi bi-chevron-left"></i>
                                    </button>
                                    <button class="btn btn-sm neumorphic-btn" id="categoriesLastBtn" onclick="goToCategoriesPage(app.pagination.categories.totalPages)" disabled>
                                        <i class="bi bi-chevron-double-left"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات الخامات والتصنيفات -->
                <div class="row">
                    <div class="col-md-3">
                        <div class="stat-card neumorphic-card">
                            <div class="stat-icon bg-primary">
                                <i class="bi bi-tags"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="totalMaterials">0</h3>
                                <p>إجمالي الخامات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card neumorphic-card">
                            <div class="stat-icon bg-info">
                                <i class="bi bi-collection"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="totalCategories">0</h3>
                                <p>إجمالي التصنيفات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card neumorphic-card">
                            <div class="stat-icon bg-success">
                                <i class="bi bi-check-circle"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="activeMaterials">0</h3>
                                <p>خامات مستخدمة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card neumorphic-card">
                            <div class="stat-icon bg-warning">
                                <i class="bi bi-exclamation-circle"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="activeCategories">0</h3>
                                <p>تصنيفات مستخدمة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الإعدادات -->
            <div id="settings" class="content-section">
                <div class="page-header">
                    <h1><i class="bi bi-gear me-2"></i>الإعدادات</h1>
                </div>

                <div class="row">
                    <!-- إعدادات المستخدم -->
                    <div class="col-md-6 mb-4">
                        <div class="neumorphic-card">
                            <h5><i class="bi bi-person-gear me-2"></i>إعدادات المستخدم</h5>
                            <form id="userSettingsForm">
                                <div class="mb-3">
                                    <label class="form-label">كلمة المرور الحالية</label>
                                    <input type="password" class="form-control neumorphic-input" id="currentPassword">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">كلمة المرور الجديدة</label>
                                    <input type="password" class="form-control neumorphic-input" id="newPassword">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">تأكيد كلمة المرور</label>
                                    <input type="password" class="form-control neumorphic-input" id="confirmPassword">
                                </div>
                                <button type="submit" class="btn btn-primary neumorphic-btn">
                                    <i class="bi bi-check-circle me-2"></i>تحديث كلمة المرور
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- إدارة المستخدمين (للمدير فقط) -->
                    <div class="col-md-6 mb-4" id="userManagement">
                        <div class="neumorphic-card">
                            <h5><i class="bi bi-people-fill me-2"></i>إدارة المستخدمين</h5>
                            <button class="btn btn-success neumorphic-btn mb-3" onclick="showAddUserModal()">
                                <i class="bi bi-person-plus me-2"></i>إضافة مستخدم جديد
                            </button>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>اسم المستخدم</th>
                                            <th>الدور</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="usersTable">
                                        <!-- سيتم ملء البيانات بواسطة JavaScript -->
                                    </tbody>
                                </table>
                            </div>

                            <!-- نظام الصفحات للمستخدمين -->
                            <div class="pagination-container d-flex justify-content-between align-items-center" id="usersPagination" style="display: none;">
                                <div class="pagination-info">
                                    <i class="bi bi-people me-2"></i>
                                    عرض <span id="usersShowingStart">1</span> إلى <span id="usersShowingEnd">5</span> من <span id="usersTotalItems">0</span> مستخدم
                                </div>
                                <div class="pagination-controls d-flex align-items-center">
                                    <button class="btn btn-sm neumorphic-btn me-1" id="usersFirstBtn" onclick="goToUsersPage(1)" disabled>
                                        <i class="bi bi-chevron-double-right"></i>
                                    </button>
                                    <button class="btn btn-sm neumorphic-btn" id="usersPrevBtn" onclick="app.changeUsersPage(-1)" disabled>
                                        <i class="bi bi-chevron-right"></i> السابق
                                    </button>
                                    <span class="mx-2 text-muted">
                                        صفحة <span id="usersCurrentPage">1</span> من <span id="usersTotalPages">1</span>
                                    </span>
                                    <button class="btn btn-sm neumorphic-btn me-1" id="usersNextBtn" onclick="app.changeUsersPage(1)" disabled>
                                        التالي <i class="bi bi-chevron-left"></i>
                                    </button>
                                    <button class="btn btn-sm neumorphic-btn" id="usersLastBtn" onclick="goToUsersPage(app.pagination.users.totalPages)" disabled>
                                        <i class="bi bi-chevron-double-left"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات النظام -->
                    <div class="col-md-12 mb-4">
                        <div class="neumorphic-card">
                            <h5><i class="bi bi-database me-2"></i>إدارة البيانات</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <button class="btn btn-info neumorphic-btn w-100 mb-2" onclick="exportData()">
                                        <i class="bi bi-download me-2"></i>تصدير جميع البيانات
                                    </button>
                                </div>
                                <div class="col-md-4">
                                    <input type="file" class="form-control neumorphic-input mb-2" id="importFile" accept=".json">
                                    <button class="btn btn-warning neumorphic-btn w-100" onclick="importData()">
                                        <i class="bi bi-upload me-2"></i>استيراد البيانات
                                    </button>
                                </div>
                                <div class="col-md-4">
                                    <button class="btn btn-danger neumorphic-btn w-100 mb-2" onclick="showFactoryResetModal()">
                                        <i class="bi bi-arrow-clockwise me-2"></i>إعادة ضبط المصنع
                                    </button>
                                    <small class="text-muted d-block">سيتم مسح جميع البيانات نهائياً</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة تأكيد إعادة ضبط المصنع -->
    <div class="modal fade" id="factoryResetModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content neumorphic-card">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title"><i class="bi bi-exclamation-triangle me-2"></i>تحذير: إعادة ضبط المصنع</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        <strong>تحذير!</strong> هذا الإجراء سيقوم بمسح جميع البيانات نهائياً ولا يمكن التراجع عنه.
                    </div>
                    <p>سيتم حذف:</p>
                    <ul>
                        <li>جميع المنتجات والمخزون</li>
                        <li>جميع العمال وسجلات الحضور</li>
                        <li>جميع سجلات المرتبات</li>
                        <li>جميع سجلات سحب العمال</li>
                        <li>جميع الخامات والتصنيفات</li>
                        <li>جميع المستخدمين (عدا المدير الرئيسي)</li>
                    </ul>
                    <form id="factoryResetForm">
                        <div class="mb-3">
                            <label class="form-label">لتأكيد العملية، اكتب "إعادة ضبط المصنع" في الحقل أدناه:</label>
                            <input type="text" class="form-control neumorphic-input" id="resetConfirmation"
                                   placeholder="إعادة ضبط المصنع" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">كلمة مرور المدير للتأكيد:</label>
                            <input type="password" class="form-control neumorphic-input" id="adminPasswordConfirm"
                                   placeholder="كلمة مرور المدير" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger" onclick="confirmFactoryReset()">
                        <i class="bi bi-arrow-clockwise me-2"></i>تأكيد إعادة الضبط
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة تأكيد إعادة ضبط المصنع -->
    <div class="modal fade" id="factoryResetModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content neumorphic-card">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title"><i class="bi bi-exclamation-triangle me-2"></i>تحذير: إعادة ضبط المصنع</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        <strong>تحذير!</strong> هذا الإجراء سيقوم بمسح جميع البيانات نهائياً ولا يمكن التراجع عنه.
                    </div>
                    <p>سيتم حذف:</p>
                    <ul>
                        <li>جميع المنتجات والمخزون</li>
                        <li>جميع العمال وسجلات الحضور</li>
                        <li>جميع سجلات المرتبات</li>
                        <li>جميع سجلات سحب العمال</li>
                        <li>جميع الخامات والتصنيفات</li>
                        <li>جميع المستخدمين (عدا المدير الرئيسي)</li>
                    </ul>
                    <form id="factoryResetForm">
                        <div class="mb-3">
                            <label class="form-label">لتأكيد العملية، اكتب "إعادة ضبط المصنع" في الحقل أدناه:</label>
                            <input type="text" class="form-control neumorphic-input" id="resetConfirmation"
                                   placeholder="إعادة ضبط المصنع" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">كلمة مرور المدير للتأكيد:</label>
                            <input type="password" class="form-control neumorphic-input" id="adminPasswordConfirm"
                                   placeholder="كلمة مرور المدير" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger" onclick="confirmFactoryReset()">
                        <i class="bi bi-arrow-clockwise me-2"></i>تأكيد إعادة الضبط
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- النوافذ المنبثقة -->

    <!-- نافذة إضافة خامة جديدة -->
    <div class="modal fade" id="addMaterialModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content neumorphic-card">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-plus-circle me-2"></i>إضافة خامة جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addMaterialForm">
                        <div class="mb-3">
                            <label class="form-label">اسم الخامة</label>
                            <input type="text" class="form-control neumorphic-input" id="materialName" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الوصف</label>
                            <textarea class="form-control neumorphic-input" id="materialDescription" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary neumorphic-btn" onclick="addMaterial()">
                        <i class="bi bi-check-circle me-2"></i>إضافة الخامة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة تعديل خامة -->
    <div class="modal fade" id="editMaterialModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content neumorphic-card">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-pencil me-2"></i>تعديل الخامة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editMaterialForm">
                        <input type="hidden" id="editMaterialId">
                        <div class="mb-3">
                            <label class="form-label">اسم الخامة</label>
                            <input type="text" class="form-control neumorphic-input" id="editMaterialName" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الوصف</label>
                            <textarea class="form-control neumorphic-input" id="editMaterialDescription" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary neumorphic-btn" onclick="updateMaterial()">
                        <i class="bi bi-check-circle me-2"></i>حفظ التعديلات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة تصنيف جديد -->
    <div class="modal fade" id="addCategoryModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content neumorphic-card">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-plus-circle me-2"></i>إضافة تصنيف جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addCategoryForm">
                        <div class="mb-3">
                            <label class="form-label">اسم التصنيف</label>
                            <input type="text" class="form-control neumorphic-input" id="categoryName" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الوصف</label>
                            <textarea class="form-control neumorphic-input" id="categoryDescription" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary neumorphic-btn" onclick="addCategory()">
                        <i class="bi bi-check-circle me-2"></i>إضافة التصنيف
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة تعديل تصنيف -->
    <div class="modal fade" id="editCategoryModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content neumorphic-card">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-pencil me-2"></i>تعديل التصنيف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editCategoryForm">
                        <input type="hidden" id="editCategoryId">
                        <div class="mb-3">
                            <label class="form-label">اسم التصنيف</label>
                            <input type="text" class="form-control neumorphic-input" id="editCategoryName" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الوصف</label>
                            <textarea class="form-control neumorphic-input" id="editCategoryDescription" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary neumorphic-btn" onclick="updateCategory()">
                        <i class="bi bi-check-circle me-2"></i>حفظ التعديلات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة منتج جديد -->
    <div class="modal fade" id="addProductModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content neumorphic-card">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-bag-plus me-2"></i>إضافة منتج جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addProductForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">كود المنتج</label>
                                <input type="text" class="form-control neumorphic-input" id="productCode" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اسم المنتج</label>
                                <input type="text" class="form-control neumorphic-input" id="productName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">نوع الخامة</label>
                                <select class="form-select neumorphic-input" id="productMaterial" required>
                                    <option value="">اختر نوع الخامة</option>
                                    <option value="جلد طبيعي">جلد طبيعي</option>
                                    <option value="جلد صناعي">جلد صناعي</option>
                                    <option value="قماش">قماش</option>
                                    <option value="بلاستيك">بلاستيك</option>
                                    <option value="مختلط">مختلط</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">التصنيف</label>
                                <select class="form-select neumorphic-input" id="productCategory" required>
                                    <option value="">اختر التصنيف</option>
                                    <option value="شنط يد">شنط يد</option>
                                    <option value="شنط ظهر">شنط ظهر</option>
                                    <option value="شنط سفر">شنط سفر</option>
                                    <option value="شنط مدرسية">شنط مدرسية</option>
                                    <option value="شنط رياضية">شنط رياضية</option>
                                    <option value="محافظ">محافظ</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الاستايل</label>
                                <input type="text" class="form-control neumorphic-input" id="productStyle">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">السعر (ج.م)</label>
                                <input type="number" class="form-control neumorphic-input" id="productPrice" step="0.01" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الكمية الأولية</label>
                                <input type="number" class="form-control neumorphic-input" id="productQuantity" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الباركود</label>
                                <div class="input-group">
                                    <input type="text" class="form-control neumorphic-input" id="productBarcode" placeholder="امسح الباركود أو أدخله يدوياً">
                                    <button type="button" class="btn btn-outline-primary" id="scanBarcodeBtn" onclick="startBarcodeScanner()" title="تفعيل الماسح الضوئي">
                                        <i class="bi bi-upc-scan"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="generateBarcode()" title="توليد باركود تلقائي">
                                        <i class="bi bi-arrow-clockwise"></i>
                                    </button>
                                </div>
                                <small class="text-muted" id="scannerStatus" style="display: none;">
                                    <i class="bi bi-circle-fill text-success me-1"></i>
                                    الماسح الضوئي نشط - امسح الباركود الآن
                                </small>
                            </div>
                            <div class="col-md-12 mb-3">
                                <label class="form-label">وصف المنتج</label>
                                <textarea class="form-control neumorphic-input" id="productDescription" rows="3"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary neumorphic-btn" onclick="addProduct()">
                        <i class="bi bi-check-circle me-2"></i>إضافة المنتج
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة عامل جديد -->
    <div class="modal fade" id="addWorkerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content neumorphic-card">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-person-plus me-2"></i>إضافة عامل جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addWorkerForm">
                        <div class="mb-3">
                            <label class="form-label">اسم العامل</label>
                            <input type="text" class="form-control neumorphic-input" id="workerName" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الوظيفة</label>
                            <select class="form-select neumorphic-input" id="workerJob" required>
                                <option value="">اختر الوظيفة</option>
                                <option value="خياط">خياط</option>
                                <option value="قصاص">قصاص</option>
                                <option value="مشرف">مشرف</option>
                                <option value="عامل تشطيب">عامل تشطيب</option>
                                <option value="عامل تعبئة">عامل تعبئة</option>
                                <option value="سائق">سائق</option>
                                <option value="حارس">حارس</option>
                                <option value="عامل نظافة">عامل نظافة</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الراتب اليومي (ج.م)</label>
                            <input type="number" class="form-control neumorphic-input" id="workerSalary" step="0.01" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control neumorphic-input" id="workerPhone">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">العنوان</label>
                            <textarea class="form-control neumorphic-input" id="workerAddress" rows="2"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">تاريخ التوظيف</label>
                            <input type="date" class="form-control neumorphic-input" id="workerHireDate" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary neumorphic-btn" onclick="addWorker()">
                        <i class="bi bi-check-circle me-2"></i>إضافة العامل
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة كمية للمخزون -->
    <div class="modal fade" id="addStockModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content neumorphic-card">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-plus-circle me-2"></i>إضافة كمية للمخزون</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addStockForm">
                        <div class="mb-3">
                            <label class="form-label">اختر المنتج</label>
                            <select class="form-select neumorphic-input" id="stockProductSelect" required>
                                <option value="">اختر المنتج</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الكمية المضافة</label>
                            <input type="number" class="form-control neumorphic-input" id="stockQuantity" min="1" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">تاريخ العملية</label>
                            <input type="date" class="form-control neumorphic-input" id="stockDate" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">نوع العملية</label>
                            <select class="form-select neumorphic-input" id="stockOperation" required>
                                <option value="add">إضافة للمخزون</option>
                                <option value="remove">خصم من المخزون</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control neumorphic-input" id="stockNotes" rows="2" placeholder="سبب الإضافة أو الخصم..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success neumorphic-btn" onclick="processStockOperation()">
                        <i class="bi bi-check-circle me-2"></i>تنفيذ العملية
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة سحب عامل من المخزون -->
    <div class="modal fade" id="workerWithdrawalModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content neumorphic-card">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-person-dash me-2"></i>سحب عامل من المخزون</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="workerWithdrawalForm">
                        <div class="mb-3">
                            <label class="form-label">اختر العامل</label>
                            <select class="form-select neumorphic-input" id="withdrawalWorkerSelect" required>
                                <option value="">اختر العامل</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">اختر المنتج</label>
                            <select class="form-select neumorphic-input" id="withdrawalProductSelect" required>
                                <option value="">اختر المنتج</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الكمية المطلوبة</label>
                            <input type="number" class="form-control neumorphic-input" id="withdrawalQuantity" min="1" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control neumorphic-input" id="withdrawalNotes" rows="2" placeholder="سبب السحب أو ملاحظات إضافية..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-warning neumorphic-btn" onclick="app.processWorkerWithdrawal()">
                        <i class="bi bi-check-circle me-2"></i>تنفيذ السحب
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة مستخدم جديد -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content neumorphic-card">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-person-plus me-2"></i>إضافة مستخدم جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addUserForm">
                        <div class="mb-3">
                            <label class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control neumorphic-input" id="newUsername" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الاسم الكامل</label>
                            <input type="text" class="form-control neumorphic-input" id="newUserFullName" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control neumorphic-input" id="newUserPassword" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الدور</label>
                            <select class="form-select neumorphic-input" id="newUserRole" required>
                                <option value="">اختر الدور</option>
                                <option value="user">مستخدم عادي</option>
                                <option value="admin">مدير</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الصلاحيات</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="permProducts">
                                        <label class="form-check-label" for="permProducts">إدارة المنتجات</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="permInventory">
                                        <label class="form-check-label" for="permInventory">إدارة المخزون</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="permWorkers">
                                        <label class="form-check-label" for="permWorkers">إدارة العمال</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="permAttendance">
                                        <label class="form-check-label" for="permAttendance">الحضور والانصراف</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="permPayroll">
                                        <label class="form-check-label" for="permPayroll">المرتبات</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="permReports">
                                        <label class="form-check-label" for="permReports">التقارير</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary neumorphic-btn" onclick="addUser()">
                        <i class="bi bi-check-circle me-2"></i>إضافة المستخدم
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة مفتاح الترخيص -->
    <div class="modal fade" id="licenseModal" tabindex="-1" aria-labelledby="licenseModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content neumorphic-card">
                <div class="modal-header">
                    <h5 class="modal-title" id="licenseModalLabel">
                        <i class="bi bi-key-fill me-2"></i>
                        إدارة الترخيص
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- حالة الترخيص -->
                    <div id="licenseStatusAlert" class="alert alert-warning">
                        <h6 id="licenseStatusTitle">لم يتم التفعيل</h6>
                        <p id="licenseStatusMessage" class="mb-0">لم يتم تفعيل أي ترخيص</p>
                    </div>

                    <!-- بطاقة معلومات العميل -->
                    <div id="customerInfoCard" class="card neumorphic-card mb-3" style="display: none;">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="bi bi-person-fill me-2"></i>
                                معلومات المشترك
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>الاسم:</strong> <span id="customerName">-</span></p>
                                    <p><strong>الهاتف:</strong> <span id="customerPhone">-</span></p>
                                    <p><strong>البريد الإلكتروني:</strong> <span id="customerEmail">-</span></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>تاريخ التفعيل:</strong> <span id="activationDate">-</span></p>
                                    <p><strong>تاريخ الانتهاء:</strong> <span id="expirationDate">-</span></p>
                                    <p><strong>المدة المتبقية:</strong> <span id="remainingDays">-</span></p>
                                </div>
                            </div>
                            <div class="text-center mt-3">
                                <button type="button" class="btn btn-danger" onclick="showCancelSubscriptionModal()">
                                    <i class="bi bi-x-circle me-2"></i>
                                    إلغاء الاشتراك
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- بطاقة إدخال الترخيص -->
                    <div id="licenseInputCard" class="card neumorphic-card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="bi bi-key me-2"></i>
                                تفعيل الترخيص
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="licenseKey" class="form-label">مفتاح الترخيص</label>
                                <textarea class="form-control neumorphic-input" id="licenseKey" rows="4"
                                         placeholder="الصق مفتاح الترخيص هنا..."></textarea>
                                <small class="text-muted">
                                    <i class="bi bi-info-circle me-1"></i>
                                    احصل على مفتاح الترخيص من المطور: 01100693019
                                </small>
                            </div>
                            <div class="text-center">
                                <button type="button" class="btn btn-success me-2" onclick="activateLicense()">
                                    <i class="bi bi-check-circle me-2"></i>
                                    تفعيل الترخيص
                                </button>
                                <button type="button" id="trialBtn" class="btn btn-info" onclick="startTrialPeriod()">
                                    <i class="bi bi-clock me-2"></i>
                                    فترة تجريبية (24 ساعة)
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- بطاقة المفاتيح الملغاة -->
                    <div id="cancelledKeysCard" class="card neumorphic-card mt-3" style="display: none;">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="bi bi-x-circle me-2 text-danger"></i>
                                المفاتيح الملغاة
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-warning">
                                <i class="bi bi-info-circle me-2"></i>
                                <strong>ملاحظة:</strong> المفاتيح الملغاة لا يمكن إعادة تفعيلها مرة أخرى
                            </div>
                            <div id="cancelledKeysList">
                                <!-- سيتم ملء قائمة المفاتيح الملغاة هنا -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة تأكيد إلغاء الاشتراك -->
    <div class="modal fade" id="cancelSubscriptionModal" tabindex="-1" aria-labelledby="cancelSubscriptionModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content neumorphic-card">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="cancelSubscriptionModalLabel">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        تأكيد إلغاء الاشتراك
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger">
                        <h6><i class="bi bi-warning me-2"></i>تحذير مهم</h6>
                        <p class="mb-0">
                            سيؤدي إلغاء الاشتراك إلى:
                        </p>
                        <ul class="mt-2 mb-0">
                            <li>فقدان جميع بيانات الترخيص</li>
                            <li>الخروج الفوري من البرنامج</li>
                            <li>عدم إمكانية التراجع عن هذا الإجراء</li>
                        </ul>
                    </div>

                    <div class="mb-3">
                        <label for="cancelConfirmationInput" class="form-label">
                            للتأكيد، اكتب كلمة <strong>"إلغاء"</strong> في الحقل أدناه:
                        </label>
                        <input type="text" class="form-control" id="cancelConfirmationInput"
                               placeholder="اكتب: إلغاء">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x me-2"></i>
                        إلغاء
                    </button>
                    <button type="button" id="confirmCancelBtn" class="btn btn-danger"
                            onclick="confirmCancelSubscription()" disabled>
                        <i class="bi bi-trash me-2"></i>
                        تأكيد الإلغاء
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container-fluid">
            <div class="text-center">
                <small class="text-muted">
                    تصميم وإعداد: البشمهندس أحمد يونس | الهاتف: 01100693019
                </small>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="js/license-system.js"></script>
    <script src="js/app.js"></script>
    <script src="js/features.js"></script>
    <script src="js/reports.js"></script>
</body>
</html>
