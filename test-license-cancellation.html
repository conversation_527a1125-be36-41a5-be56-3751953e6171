<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إلغاء الاشتراك</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .danger {
            background: #dc3545;
        }
        .danger:hover {
            background: #c82333;
        }
        .success {
            background: #28a745;
        }
        .success:hover {
            background: #218838;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success-result {
            border-left-color: #28a745;
            background: #d4edda;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار نظام إلغاء الاشتراك</h1>
        <p>هذه الصفحة لاختبار أن مفاتيح الترخيص لا تعمل بعد إلغاء الاشتراك</p>

        <div class="test-section">
            <h3>1. إنشاء ترخيص تجريبي</h3>
            <button onclick="createTestLicense()" class="success">إنشاء ترخيص تجريبي</button>
            <div id="createResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. تفعيل الترخيص</h3>
            <textarea id="testLicenseKey" placeholder="الصق مفتاح الترخيص هنا..." style="width: 100%; height: 100px; margin: 10px 0;"></textarea>
            <button onclick="activateTestLicense()">تفعيل الترخيص</button>
            <div id="activateResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. إلغاء الاشتراك</h3>
            <button onclick="cancelSubscription()" class="danger">إلغاء الاشتراك</button>
            <div id="cancelResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>4. محاولة إعادة تفعيل نفس المفتاح</h3>
            <button onclick="retryActivation()">محاولة إعادة التفعيل</button>
            <div id="retryResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>5. عرض المفاتيح المستخدمة</h3>
            <button onclick="showUsedKeys()">عرض المفاتيح المستخدمة</button>
            <div id="usedKeysResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script src="js/license-system.js"></script>
    <script>
        // إنشاء نسخة من نظام الترخيص للاختبار
        const testLicenseSystem = new LicenseSystem();
        let currentTestKey = '';

        function showResult(elementId, message, isError = false, isSuccess = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.innerHTML = message;
            element.className = 'result';
            if (isError) element.className += ' error';
            if (isSuccess) element.className += ' success-result';
        }

        function createTestLicense() {
            // إنشاء ترخيص تجريبي بسيط
            const testData = {
                customerName: 'عميل تجريبي',
                customerPhone: '01234567890',
                customerEmail: '<EMAIL>',
                licenseType: 'annual',
                expirationDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
                licenseId: 'TEST_' + Date.now()
            };

            // تشفير بسيط للاختبار
            const deviceKey = testLicenseSystem.generateDeviceKey();
            const encrypted = testLicenseSystem.encrypt(JSON.stringify(testData), deviceKey);
            
            currentTestKey = encrypted;
            document.getElementById('testLicenseKey').value = encrypted;
            
            showResult('createResult', 'تم إنشاء ترخيص تجريبي بنجاح!', false, true);
        }

        function activateTestLicense() {
            const licenseKey = document.getElementById('testLicenseKey').value.trim();
            if (!licenseKey) {
                showResult('activateResult', 'يرجى إدخال مفتاح الترخيص', true);
                return;
            }

            currentTestKey = licenseKey;
            const result = testLicenseSystem.activateLicense(licenseKey);
            
            if (result.success) {
                showResult('activateResult', 'تم تفعيل الترخيص بنجاح!', false, true);
            } else {
                showResult('activateResult', 'فشل في التفعيل: ' + result.message, true);
            }
        }

        function cancelSubscription() {
            if (!testLicenseSystem.licenseData.isActivated) {
                showResult('cancelResult', 'لا يوجد اشتراك مفعل لإلغائه', true);
                return;
            }

            const result = testLicenseSystem.clearLicenseData();
            
            if (result.success) {
                showResult('cancelResult', 'تم إلغاء الاشتراك بنجاح!', false, true);
            } else {
                showResult('cancelResult', 'فشل في إلغاء الاشتراك: ' + result.message, true);
            }
        }

        function retryActivation() {
            if (!currentTestKey) {
                showResult('retryResult', 'لا يوجد مفتاح للاختبار', true);
                return;
            }

            const result = testLicenseSystem.activateLicense(currentTestKey);
            
            if (result.success) {
                showResult('retryResult', 'تم إعادة تفعيل الترخيص! (هذا خطأ - يجب أن يفشل)', true);
            } else {
                showResult('retryResult', 'فشل في إعادة التفعيل (هذا صحيح): ' + result.message, false, true);
            }
        }

        function showUsedKeys() {
            const usedKeys = testLicenseSystem.loadUsedLicenseKeys();
            
            if (usedKeys.length === 0) {
                showResult('usedKeysResult', 'لا توجد مفاتيح مستخدمة');
                return;
            }

            let html = '<h4>المفاتيح المستخدمة:</h4>';
            usedKeys.forEach((key, index) => {
                html += `<div style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 5px;">`;
                html += `<strong>مفتاح ${index + 1}:</strong> ${key.keyPreview}<br>`;
                html += `<strong>تاريخ الاستخدام:</strong> ${new Date(key.usedDate).toLocaleString('ar-EG')}<br>`;
                html += `<strong>الحالة:</strong> ${key.status || 'نشط'}<br>`;
                if (key.status === 'cancelled') {
                    html += `<strong>تاريخ الإلغاء:</strong> ${new Date(key.cancelledDate).toLocaleString('ar-EG')}<br>`;
                    html += `<strong>سبب الإلغاء:</strong> ${key.reason}<br>`;
                }
                html += `</div>`;
            });

            showResult('usedKeysResult', html);
        }
    </script>
</body>
</html>
